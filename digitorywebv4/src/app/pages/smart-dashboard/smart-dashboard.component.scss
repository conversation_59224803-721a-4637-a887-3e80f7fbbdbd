.smart-dashboard-container {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  height: 100%; // Take full available height from parent dashboard
  overflow: hidden; // Prevent outer scrollbar, let internal areas handle scrolling

  // ===== MATERIAL DESIGN THEME OVERRIDES =====
  ::ng-deep {
    // Form fields
    .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper {
        height: 36px;
        min-height: 36px;
      }

      .mat-mdc-form-field-infix {
        padding: 6px 12px;
        min-height: 24px;
        border-top: none;
      }

      .mat-mdc-form-field-flex {
        align-items: center;
        height: 36px;
      }

      .mat-mdc-form-field-subscript-wrapper {
        display: none;
      }

      .mat-mdc-form-field-outline {
        color: #dee2e6;
      }

      .mat-mdc-form-field-outline-thick {
        color: #ffb366;
      }

      .mat-mdc-form-field-label {
        color: #666;
        font-size: 13px;
        top: 18px;
      }

      &.mat-focused .mat-mdc-form-field-label {
        color: #ffb366;
      }

      &.mat-form-field-should-float .mat-mdc-form-field-label {
        transform: translateY(-12px) scale(0.75);
      }
    }

    // Select dropdowns
    .mat-mdc-select {
      .mat-mdc-select-trigger {
        height: 36px;
        display: flex;
        align-items: center;
      }

      .mat-mdc-select-value {
        font-size: 13px;
        line-height: 24px;
      }

      .mat-mdc-select-arrow {
        color: #ffb366;
      }
    }

    // Select panel
    .mat-mdc-select-panel .mat-mdc-option {
      height: 32px;
      line-height: 32px;
      font-size: 13px;
      padding: 0 16px;

      &.mat-mdc-option-active {
        background: rgba(255, 179, 102, 0.1);
        color: #ffb366;
      }

      &:hover {
        background: rgba(255, 179, 102, 0.05);
      }
    }

    // Input elements
    .mat-mdc-input-element {
      font-size: 13px;
      height: 24px;
      line-height: 24px;
    }

    // Date picker
    .mat-datepicker-toggle .mat-icon {
      color: #ffb366;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .mat-datepicker-content {
      .mat-calendar-header {
        background: #ffb366;
        color: white;
      }

      .mat-calendar-body-selected {
        background-color: #ffb366;
        color: white;
      }

      .mat-calendar-body-today:not(.mat-calendar-body-selected) {
        border-color: #ffb366;
      }
    }

    // Buttons
    .mat-mdc-raised-button,
    .mat-mdc-outlined-button {
      height: 32px;
      line-height: 32px;
      padding: 0 12px;
      font-size: 13px;

      &.mat-primary {
        background-color: #ffb366;
        color: white;

        &:hover {
          background-color: #ffa64d;
        }
      }
    }

    .mat-mdc-outlined-button.mat-primary {
      border-color: #ffb366;
      color: #ffb366;
      background-color: transparent;

      &:hover {
        background-color: rgba(255, 179, 102, 0.05);
      }
    }
  }

  // Main Layout Container
  .main-layout {
    display: flex;
    height: 100%; // Use full available height from parent container
    overflow: hidden; // Prevent outer scrollbar

    // Left Sidebar
    .left-sidebar {
      width: 250px;
      background: linear-gradient(135deg, #ffffff 0%, #fff5f0 100%);
      border-right: 1px solid #ffe0cc;
      padding: 12px 12px 60px 12px; // Add bottom padding for toggle switch
      box-shadow: 2px 0 4px rgba(255, 179, 102, 0.08);
      height: 100%; // Take full available height
      overflow-y: auto; // Allow scrolling within sidebar if needed
      position: relative; // For absolute positioned toggle switch

      // Custom scrollbar styling - default gray colors
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;

        &:hover {
          background: rgba(0, 0, 0, 0.3);
        }
      }

      // Dashboard Selection
      .dashboard-selection {
        margin-bottom: 20px;

        .dashboard-dropdown {
          width: 100%;

          ::ng-deep .mat-mdc-text-field-wrapper {
            background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
            border-radius: 6px;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

            &:hover {
              border-color: #ffb366;
              box-shadow: 0 2px 6px rgba(255, 179, 102, 0.1);
            }
          }

          ::ng-deep .mat-mdc-form-field-outline,
          ::ng-deep .mat-mdc-form-field-outline-thick {
            display: none;
          }

          ::ng-deep .mat-focused .mat-mdc-text-field-wrapper {
            border-color: #ffb366;
            box-shadow: 0 3px 8px rgba(255, 179, 102, 0.15);
          }
        }
      }

      // Filters Section
      .filters-section {
        .filters-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0 0 16px 0;
          font-size: 15px;
          font-weight: 600;
          color: #333;

          mat-icon {
            color: #ffb366;
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }

        .filter-group {
          margin-bottom: 15px;

          .filter-label {
            margin: 0 0 8px 0;
            font-size: 13px;
            font-weight: 500;
            color: #333;
          }

          .filter-field {
            width: 100%;
            margin-bottom: 6px;

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: white;
              border-radius: 4px;
            }
          }
        }

        .date-range-group {
          display: flex;
          flex-direction: column;
        }



        .filter-actions {
          margin-top: 20px;
          padding-top: 16px;
          border-top: 1px solid #e9ecef;
          display: flex;
          gap: 8px;

          .search-btn,
          .reset-filters-btn {
            flex: 1;
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }

          .search-btn {
            background-color: white;
            color: #ffb366;
            border: 2px solid #ffb366;
            font-weight: 600;

            &:hover {
              background-color: #fff8f5;
              border-color: #ffa64d;
              color: #ffa64d;
            }
          }

          .reset-filters-btn {
            color: #6c757d;
            border-color: #dee2e6;

            &:hover {
              background-color: #f8f9fa;
              border-color: #adb5bd;
            }
          }
        }
      }
    }

    // Dashboard Mode Toggle Section
    .dashboard-mode-section {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
      border-top: 2px solid #ffb366;
      padding: 16px;
      box-shadow: 0 -2px 8px rgba(255, 179, 102, 0.15);

      .mode-label {
        font-size: 13px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 8px;

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
          color: #ffb366;
        }
      }

      .mode-toggle-container {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .toggle-switch {
          display: flex;
          background: #f8f9fa;
          border: 2px solid #e9ecef;
          border-radius: 12px;
          padding: 4px;
          position: relative;
          overflow: hidden;

          .toggle-option {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;

            &.active {
              background: #ff8c42;
              color: white;
              box-shadow: 0 2px 8px rgba(255, 140, 66, 0.3);
            }

            &:not(.active) {
              color: #6c757d;
              background: transparent;

              &:hover:not(.disabled) {
                background: rgba(255, 140, 66, 0.1);
                color: #ff8c42;
              }
            }

            &.disabled {
              cursor: not-allowed;
              opacity: 0.6;

              &:hover {
                background: transparent !important;
                color: #6c757d !important;
              }
            }

            .beta-icon {
              font-size: 12px;
              width: 12px;
              height: 12px;
            }
          }

          &.ai-disabled .ai-option {
            position: relative;

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(173, 181, 189, 0.1) 2px,
                rgba(173, 181, 189, 0.1) 4px
              );
              pointer-events: none;
            }
          }
        }

        .mode-description {
          text-align: center;
          margin-top: 4px;

          .coming-soon {
            font-size: 10px;
            color: #adb5bd;
            font-style: italic;
            font-weight: 500;
          }
        }
      }
    }

    // Right Content Area
    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #f8f9fa;
      height: 100%; // Take full available height
      overflow: hidden; // Prevent outer scrollbar

      // AI Assistant Header
      .ai-assistant-header {
        background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
        border-bottom: 1px solid #e9ecef;
        padding: 10px 20px;
        display: flex;
        align-items: center;
        gap: 16px;
        min-height: 50px;
        box-shadow: 0 1px 3px rgba(255, 179, 102, 0.05);

        .assistant-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;

          .assistant-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
            transition: color 0.3s ease;

            &.disabled {
              color: #adb5bd;
            }
          }

          .assistant-text {
            display: flex;
            align-items: center;
            gap: 10px;

            .assistant-title {
              font-size: 14px;
              font-weight: 600;
              color: #333;
              white-space: nowrap;
            }

            .assistant-status {
              font-size: 10px;
              font-weight: 500;
              padding: 2px 8px;
              border-radius: 12px;
              display: inline-block;
              white-space: nowrap;

              &.disabled {
                color: #adb5bd;
                background: #f8f9fa;
              }
            }
          }
        }

        .search-container {
          flex: 1;
          min-width: 0;
          max-width: calc(100% - 200px);

          .search-field {
            width: 100%;

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: white;
              border-radius: 24px;
              height: 36px;
              border: 1px solid #e9ecef;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
              transition: all 0.3s ease;

              &:hover:not(.disabled) {
                border-color: #ffb366;
                box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);
              }
            }

            ::ng-deep .mat-mdc-form-field-outline,
            ::ng-deep .mat-mdc-form-field-outline-thick {
              display: none;
            }

            ::ng-deep .mat-mdc-form-field-infix {
              padding: 6px 16px;
              border-top: none;
            }

            ::ng-deep .mat-mdc-form-field-flex {
              align-items: center;
              height: 36px;
            }

            ::ng-deep input {
              font-size: 14px;
              color: #333;

              &::placeholder {
                color: #999;
                font-size: 14px;
              }
            }

            ::ng-deep .mat-focused .mat-mdc-text-field-wrapper {
              border-color: #ffb366;
              box-shadow: 0 4px 16px rgba(255, 179, 102, 0.15);
            }

            .search-icon {
              color: #999;
              cursor: pointer;
              font-size: 16px;
              transition: color 0.2s ease;

              &:hover:not(.disabled) {
                color: #ffb366;
              }

              &.disabled {
                color: #adb5bd;
                cursor: not-allowed;
              }
            }

            &.disabled {
              ::ng-deep .mat-mdc-text-field-wrapper {
                background-color: #f8f9fa;
                border-color: #e9ecef;
                box-shadow: none;

                &:hover {
                  border-color: #e9ecef;
                  box-shadow: none;
                }
              }

              ::ng-deep input {
                color: #adb5bd;
                cursor: not-allowed;
              }

              ::ng-deep input::placeholder {
                color: #adb5bd;
              }
            }
          }
        }
      }

      // Dashboard Content Area
      .dashboard-content-area {
        padding: 20px;
        flex: 1; // Take remaining height after search header
        overflow-y: auto; // Only this area should scroll

        // Custom scrollbar styling - default gray colors
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;

          &:hover {
            background: rgba(0, 0, 0, 0.3);
          }
        }

        .loading-container {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 500px;
          padding: 40px 20px;
          background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
          border-radius: 16px;
          border: 1px solid #ffe0cc;
          margin: 20px;
          box-shadow: 0 8px 32px rgba(255, 179, 102, 0.12);

          .loading-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 32px;
            max-width: 400px;
            text-align: center;

            .loading-animation {
              position: relative;
              width: 120px;
              height: 120px;
              display: flex;
              align-items: center;
              justify-content: center;

              .pulse-circle {
                position: absolute;
                width: 100%;
                height: 100%;
                border: 3px solid #ffb366;
                border-radius: 50%;
                opacity: 0;
                animation: pulse 2s ease-in-out infinite;

                &.delay-1 {
                  animation-delay: 0.5s;
                }

                &.delay-2 {
                  animation-delay: 1s;
                }
              }

              .loading-icon {
                font-size: 48px;
                width: 48px;
                height: 48px;
                color: #ffb366;
                z-index: 1;
                animation: float 3s ease-in-out infinite;
              }
            }

            .loading-text {
              h3 {
                margin: 0 0 8px 0;
                font-size: 24px;
                font-weight: 600;
                color: #333;
                background: linear-gradient(135deg, #ffb366 0%, #ffc999 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
              }

              p {
                margin: 0;
                font-size: 16px;
                color: #666;
                line-height: 1.5;
              }
            }
          }
        }

        // Loading Animations
        @keyframes pulse {
          0% {
            transform: scale(0.8);
            opacity: 1;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.3;
          }
          100% {
            transform: scale(1.4);
            opacity: 0;
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        .dashboard-grid {
          .summary-cards-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 20px;

            .summary-card {
              background: white;
              border-radius: 8px;
              border: 1px solid #e9ecef;
              transition: box-shadow 0.2s ease;

              &:hover {
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
              }

              .card-content {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 16px;

                .card-icon {
                  width: 40px;
                  height: 40px;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background: linear-gradient(135deg, #ffb366 0%, #ffc999 100%);

                  mat-icon {
                    font-size: 20px;
                    width: 20px;
                    height: 20px;
                    color: white;
                  }
                }

                .card-info {
                  flex: 1;

                  .card-value {
                    font-size: 20px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 4px;
                    line-height: 1.2;
                  }

                  .card-label {
                    font-size: 12px;
                    color: #6c757d;
                    font-weight: 500;
                    line-height: 1.3;
                  }
                }
              }
            }
          }

          .charts-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 16px;

            .chart-card {
              background: white;
              border-radius: 12px;
              border: 1px solid #e9ecef;
              transition: box-shadow 0.2s ease;
              height: 400px;

              &:hover {
                box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);
              }

              &.full-width {
                grid-column: span 12;
              }

              &.half-width {
                grid-column: span 6;
              }

              &.third-width {
                grid-column: span 4;
              }

              .chart-title {
                font-size: 14px;
                font-weight: 600;
                color: #333;
                margin: 0;
              }

              .chart-container {
                position: relative;
                height: 320px;
                padding: 16px;

                canvas {
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          text-align: center;
          color: #666;

          .empty-icon {
            font-size: 64px;
            width: 64px;
            height: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
          }

          h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
          }

          p {
            margin: 0 0 24px 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1400px) {
  .smart-dashboard-container .dashboard-grid {
    .summary-cards-row {
      grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid .chart-card.half-width,
    .charts-grid .chart-card.third-width {
      grid-column: span 12;
    }
  }
}

@media (max-width: 1024px) {
  .smart-dashboard-container {
    .ai-assistant-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .left-sidebar {
      width: 220px;
    }
  }
}

@media (max-width: 768px) {
  .smart-dashboard-container .main-layout {
    flex-direction: column;

    .left-sidebar {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid #e9ecef;
      padding: 12px;

      // Mobile dashboard mode section
      .dashboard-mode-section {
        position: relative;
        margin-top: 16px;
        border-top: 1px solid #e9ecef;
        border-radius: 8px;
        background: #f8f9fa;

        .mode-toggle-container {
          .toggle-switch {
            .toggle-option {
              padding: 10px 16px;
              font-size: 13px;
            }
          }
        }
      }
    }

    .right-content {
      .ai-assistant-header {
        flex-direction: column;
        gap: 12px;
        padding: 12px 16px;
      }

      .dashboard-content-area {
        padding: 12px;

        .dashboard-grid {
          .summary-cards-row {
            grid-template-columns: 1fr;
            gap: 12px;
          }

          .charts-grid {
            gap: 12px;

            .chart-card {
              grid-column: span 12;
            }
          }
        }
      }
    }
  }

  // Responsive Design for Loading State
  @media (max-width: 768px) {
    .loading-container {
      margin: 10px;
      padding: 20px 10px;
      min-height: 350px;

      .loading-content {
        gap: 24px;

        .loading-animation {
          width: 80px;
          height: 80px;

          .loading-icon {
            font-size: 32px;
            width: 32px;
            height: 32px;
          }
        }

        .loading-text {
          h3 {
            font-size: 20px;
          }

          p {
            font-size: 14px;
          }
        }
      }
    }
  }

  // ===== ENHANCED TABLE CHART STYLES FOR RECONCILIATION =====
  .chart-card {
    &:has(.table-chart) {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(255, 152, 0, 0.08);
      border: 1px solid #ffe0b2;
      overflow: hidden;

      ::ng-deep .mat-mdc-card-header {
        background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        padding: 24px;
        border-bottom: 2px solid #ffcc02;

        .mat-mdc-card-title {
          color: #e65100 !important;
          font-weight: 700 !important;
          font-size: 20px !important;
          margin: 0 !important;
          display: flex;
          align-items: center;
          gap: 10px;

          &:before {
            content: '📊';
            font-size: 18px;
          }
        }
      }

      ::ng-deep .mat-mdc-card-content {
        padding: 24px;
        background: #fafafa;
      }
    }
  }

  .table-chart {
    width: 100%;
    height: 100%;

    // Enhanced table header styling
    ::ng-deep .chart-title {
      color: #e65100 !important;
      font-weight: 700 !important;
      font-size: 20px !important;
      margin: 0 !important;
      display: flex;
      align-items: center;
      gap: 10px;

      &:before {
        content: '📊';
        font-size: 18px;
      }
    }

    .table-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 20px 24px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(255, 152, 0, 0.06);
      border: 1px solid #ffe0b2;
      gap: 16px;
      flex-wrap: wrap;

      .search-field {
        flex: 1;
        min-width: 280px;

        .mat-mdc-form-field-wrapper {
          padding-bottom: 0;
        }

        ::ng-deep .mat-mdc-form-field {
          .mat-mdc-form-field-outline {
            color: #ffe0b2;
          }

          .mat-mdc-form-field-outline-thick {
            color: #ff9800;
          }

          &.mat-focused .mat-mdc-form-field-label {
            color: #ff9800;
          }
        }

        input {
          font-size: 14px;
          color: #424242;

          &::placeholder {
            color: #9e9e9e;
          }
        }
      }

      .table-export {
        display: flex;
        gap: 12px;
        align-items: center;

        button {
          background: white;
          color: #ff9800;
          border: 2px solid #ff9800;
          border-radius: 8px;
          padding: 10px 20px;
          font-weight: 600;
          font-size: 13px;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;

          &:hover {
            background: #ff9800;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(255, 152, 0, 0.3);
          }

          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }
      }
    }

    .table-responsive {
      max-height: 650px;
      overflow-y: auto;
      border-radius: 12px;
      border: 1px solid #ffe0b2;
      box-shadow: 0 8px 32px rgba(255, 152, 0, 0.08);
      background: white;

      // PROFESSIONAL RECONCILIATION TABLE - COMPLETE REWRITE
      .reconciliation-table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 11px !important;
        font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        margin: 0 !important;
        background: white !important;
        border-radius: 6px !important;
        overflow: hidden !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
        border: 1px solid #e5e5e5 !important;

        // HEADER STYLING - FORCE OVERRIDE
        thead,
        .table-header {
          th,
          .header-cell {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
            color: white !important;
            padding: 10px 8px !important;
            text-align: center !important;
            font-weight: 700 !important;
            font-size: 10px !important;
            border: 1px solid #f57c00 !important;
            position: sticky !important;
            top: 0 !important;
            z-index: 10 !important;
            white-space: nowrap !important;
            text-transform: uppercase !important;
            letter-spacing: 0.3px !important;
            line-height: 1.2 !important;
            vertical-align: middle !important;

            &:first-child {
              text-align: left !important;
              padding-left: 12px !important;
            }
          }
        }

        // ROW STYLING - FORCE OVERRIDE
        tbody {
          tr,
          .table-row {
            background: white !important;
            transition: all 0.15s ease !important;

            &:nth-child(even) {
              background: #fafafa !important;
            }

            &:hover {
              background: #f5f5f5 !important;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            }

            // CATEGORY ROWS - ORANGE THEME
            &.category-row {
              background: #fff8f0 !important;
              font-weight: 700 !important;

              td,
              .table-cell {
                background: #fff8f0 !important;
                color: #e65100 !important;
                font-weight: 700 !important;
                font-size: 11px !important;
                padding: 10px 8px !important;
                border: 1px solid #ffe0b2 !important;
                border-left: 3px solid #ff9800 !important;
                text-transform: uppercase !important;
                letter-spacing: 0.3px !important;

                &:first-child {
                  text-align: left !important;
                  padding-left: 12px !important;
                }

                &:not(:first-child) {
                  text-align: right !important;
                  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
                }
              }
            }

            // SUBCATEGORY ROWS - CLEAN WHITE
            &.subcategory-row {
              background: white !important;

              &:nth-child(even) {
                background: #fafafa !important;
              }

              td,
              .table-cell {
                background: inherit !important;
                color: #424242 !important;
                font-size: 10px !important;
                font-weight: 500 !important;
                padding: 8px 8px !important;
                border: 1px solid #f0f0f0 !important;

                &:first-child {
                  text-align: left !important;
                  padding-left: 20px !important;
                  position: relative !important;

                  &:before {
                    content: '▸' !important;
                    color: #ff9800 !important;
                    font-weight: bold !important;
                    position: absolute !important;
                    left: 8px !important;
                    font-size: 8px !important;
                  }
                }

                &:not(:first-child) {
                  text-align: right !important;
                  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
                }
              }
            }

            // GRAND TOTAL ROWS - STRONG ORANGE
            &.grand-total-row {
              background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
              font-weight: 700 !important;
              border-top: 2px solid #e65100 !important;

              td,
              .table-cell {
                background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
                color: white !important;
                font-size: 11px !important;
                font-weight: 700 !important;
                padding: 12px 8px !important;
                border: 1px solid #f57c00 !important;
                text-transform: uppercase !important;
                letter-spacing: 0.3px !important;

                &:first-child {
                  text-align: left !important;
                  padding-left: 12px !important;
                }

                &:not(:first-child) {
                  text-align: right !important;
                  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
                }
              }
            }

            // GENERAL CELL STYLING - FORCE OVERRIDE
            td,
            .table-cell {
              padding: 8px 8px !important;
              text-align: right !important;
              border: 1px solid #f0f0f0 !important;
              vertical-align: middle !important;
              font-size: 10px !important;
              color: #424242 !important;
              background: white !important;

              &:first-child {
                text-align: left !important;
                min-width: 180px !important;
                font-weight: 500 !important;
                padding-left: 8px !important;
              }

              &:not(:first-child) {
                font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
                font-weight: 500 !important;
                text-align: right !important;
              }
            }
          }
        }
        }

        // SPECIAL CELL FORMATTING - FORCE OVERRIDE
        ::ng-deep .currency-cell {
          font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
          font-weight: 600 !important;
          color: #2e7d32 !important;
          text-align: right !important;

          &.negative {
            color: #d32f2f !important;
          }

          &.zero {
            color: #757575 !important;
            font-style: italic !important;
          }
        }

        ::ng-deep .empty-cell {
          color: #bdbdbd !important;
          font-style: italic !important;
          text-align: center !important;

          &:before {
            content: '—' !important;
          }
        }

        ::ng-deep .zero-value {
          color: #757575 !important;
          font-style: italic !important;
        }

        ::ng-deep .subcategory-item {
          padding-left: 20px !important;
          color: #424242 !important;
          position: relative !important;

          &:before {
            content: '▸' !important;
            color: #ff9800 !important;
            font-weight: bold !important;
            position: absolute !important;
            left: 8px !important;
            font-size: 8px !important;
          }
        }
      }

      // Custom scrollbar for table - Orange theme
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #fff3e0;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #ffcc02 0%, #ff9800 100%);
        border-radius: 4px;
        border: 1px solid #ffe0b2;

        &:hover {
          background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }
      }
    }

    // Pagination styles - Orange theme
    ::ng-deep .mat-mdc-paginator {
      border-top: 2px solid #ffe0b2;
      background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
      border-radius: 0 0 12px 12px;

      .mat-mdc-paginator-page-size-select {
        margin: 0 8px;
      }

      .mat-mdc-icon-button {
        color: #ff9800;

        &:hover {
          background: rgba(255, 152, 0, 0.1);
        }

        &[disabled] {
          color: #ffcc02;
        }
      }

      .mat-mdc-paginator-range-label {
        color: #e65100;
        font-weight: 500;
      }
    }

  // Responsive adjustments for tables
  @media (max-width: 1200px) {
    .table-chart .reconciliation-table {
      font-size: 10px;

      .header-cell, .table-cell {
        padding: 4px 6px;
      }
    }
  }

  @media (max-width: 768px) {
    .table-chart {
      .table-controls {
        flex-direction: column;
        gap: 12px;

        .search-field {
          width: 100%;
        }
      }

      .reconciliation-table {
        font-size: 9px;

        .header-cell {
          font-size: 8px;
          padding: 6px 4px;
        }

        .table-cell {
          padding: 4px;

          &:first-child {
            min-width: 150px;
          }
        }
      }
    }
  }
}

// Select All custom option styling
::ng-deep .select-all-custom-option {
  background-color: #f5f5f5 !important;
  padding: 0 16px;
  height: 32px;
  line-height: 32px;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #eeeeee !important;
  }

  strong {
    color: #1976d2 !important;
    font-weight: 500 !important;
  }
}