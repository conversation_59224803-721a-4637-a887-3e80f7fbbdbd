#!/usr/bin/env python3
"""
Test script for reconciliation dashboard functionality
"""
import pandas as pd
import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from utility.dashboard_agents import generate_reconciliation_dashboard

def create_sample_store_variance_data():
    """Create sample store variance data for testing"""
    data = [
        {
            'Category': 'Food',
            'Sub Category': 'Dairy & Cheese Product',
            'Location': 'Store',
            'Opening Amount': 2813,
            'Purchase Amount': 195206,
            'Closing Amount': 8145,
            'Ibt In Amount': 0,
            'Ibt Out Amount': 0,
            'Return To Store In Qty': 0,
            'WAC(incl.tax,etc)': 100,
            'Return-Qty Amount': 0,
            'Indent Amount': 0,
            'Spoilage Amount': 0
        },
        {
            'Category': 'Food',
            'Sub Category': 'Meat Product',
            'Location': 'Kitchen',
            'Opening Amount': 17548,
            'Purchase Amount': 91887,
            'Closing Amount': 13423,
            'Ibt In Amount': 0,
            'Ibt Out Amount': 0,
            'Return To Store In Qty': 0,
            'WAC(incl.tax,etc)': 100,
            'Return-Qty Amount': 0,
            'Indent Amount': 0,
            'Spoilage Amount': 0
        },
        {
            'Category': 'Liquor',
            'Sub Category': 'Imported Liquor',
            'Location': 'Bar + Store',
            'Opening Amount': 928369,
            'Purchase Amount': 98351,
            'Closing Amount': 841531,
            'Ibt In Amount': 0,
            'Ibt Out Amount': 0,
            'Return To Store In Qty': 0,
            'WAC(incl.tax,etc)': 100,
            'Return-Qty Amount': 0,
            'Indent Amount': 0,
            'Spoilage Amount': 0
        }
    ]
    return pd.DataFrame(data)

def create_sample_consumption_data():
    """Create sample consumption data for testing"""
    data = [
        {
            'Category': 'Food',
            'Sub Category': 'Dairy & Cheese Product',
            'Actual Consumption Valuation': 167482
        },
        {
            'Category': 'Food',
            'Sub Category': 'Meat Product',
            'Actual Consumption Valuation': 107234
        },
        {
            'Category': 'Liquor',
            'Sub Category': 'Imported Liquor',
            'Actual Consumption Valuation': 185189
        }
    ]
    return pd.DataFrame(data)

def test_reconciliation_dashboard():
    """Test the reconciliation dashboard generation"""
    print("🧪 Testing Reconciliation Dashboard Generation...")
    
    # Create sample data
    store_df = create_sample_store_variance_data()
    consumption_df = create_sample_consumption_data()
    
    print(f"📊 Store variance data: {len(store_df)} records")
    print(f"📊 Consumption data: {len(consumption_df)} records")
    
    # Generate dashboard
    try:
        result = generate_reconciliation_dashboard(store_df, consumption_df)
        
        if result['success']:
            print("✅ Dashboard generation successful!")
            print(f"📈 Charts generated: {len(result['charts'])}")
            print(f"📋 Summary items: {len(result['summary_items'])}")
            
            # Check if we have the reconciliation table
            table_chart = None
            for chart in result['charts']:
                if chart['type'] == 'table':
                    table_chart = chart
                    break
            
            if table_chart:
                print("✅ Reconciliation table found!")
                print(f"📊 Table title: {table_chart['title']}")
                print(f"📊 Headers: {len(table_chart['data']['headers'])}")
                print(f"📊 Rows: {len(table_chart['data']['rows'])}")
                
                # Print first few headers
                headers = table_chart['data']['headers']
                print(f"📋 Headers: {headers[:3]}...")
                
                # Print first few rows
                rows = table_chart['data']['rows']
                if rows:
                    print("📋 Sample rows:")
                    for i, row in enumerate(rows[:3]):
                        if isinstance(row, dict):
                            expense_head = row.get('Expense Head/Item', 'N/A')
                            print(f"   Row {i+1}: {expense_head}")
                        else:
                            print(f"   Row {i+1}: {row[0] if row else 'Empty'}")
                
                print("✅ Test completed successfully!")
                return True
            else:
                print("❌ No table chart found in results")
                return False
        else:
            print(f"❌ Dashboard generation failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_reconciliation_dashboard()
    sys.exit(0 if success else 1)
